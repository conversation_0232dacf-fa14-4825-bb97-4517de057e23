import { defineComponent, onUnmounted, reactive } from 'vue'
import Draw from './draw'

// 声明类型
const PropsType = {
  warehouseData: {
    type: Object,
    required: true,
  },
} as const

export default defineComponent({
  props: PropsType,
  components: {
    Draw,
  },
  setup(props) {
    let intervalInstance = null
    const cdata = reactive({
      warehouseData: props.warehouseData,
    })

    intervalInstance = setInterval(() => {
      // 可以在这里更新数据
    }, 1000)

    onUnmounted(() => {
      clearInterval(intervalInstance)
    })

    return () => {
      return (
        <div>
          <Draw cdata={cdata} />
        </div>
      )
    }
  },
})
