import * as CryptoJS from 'crypto-js'

const other = {
  adaptationUrl: (url?: string) => {
    return adaptationUrl(url)
  },
  decryption: (src: string, keyWord: string) => {
    return decryption(src, keyWord)
  },
  encryption: (src: string, keyWord: string) => {
    return encryption(src, keyWord)
  },
}

/**
 * 自动适配不同的后端架构
 * 1. 例如 /act/oa/task ,在微服务架构保持不变,在单体架构编程 /admin/oa/task
 * 2. 特殊 /gen/xxx ,在微服务架构、单体架构编程 都需保持不变
 *
 * @param originUrl 原始路径
 */
const adaptationUrl = (originUrl?: string) => {
  // 微服务架构 不做路径转换,为空不做路径转换
  const isMicro = process.env.VUE_APP_IS_MICRO
  if (validateNull(isMicro) || isMicro === 'true') {
    return originUrl
  }

  return `/admin/${originUrl?.split('/').splice(2).join('/')}`
}
export function decryption(src: string, keyWord: string) {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  // 解密逻辑
  var decryptd = CryptoJS.AES.decrypt(src, key, {
    iv: key,
    mode: CryptoJS.mode.CFB,
    padding: CryptoJS.pad.NoPadding,
  })

  return decryptd.toString(CryptoJS.enc.Utf8)
}

export function encryption(src: string, keyWord: string) {
  const key = CryptoJS.enc.Utf8.parse(keyWord)
  // 加密
  var encrypted = CryptoJS.AES.encrypt(src, key, {
    iv: key,
    mode: CryptoJS.mode.CFB,
    padding: CryptoJS.pad.NoPadding,
  })
  return encrypted.toString()
}
export const validateNull = (val: any) => {
  if (typeof val === 'boolean') {
    return false
  }
  if (typeof val === 'number') {
    return false
  }
  if (val instanceof Array) {
    if (val.length === 0) return true
  } else if (val instanceof Object) {
    if (JSON.stringify(val) === '{}') return true
  } else {
    if (
      val === 'null' ||
      val == null ||
      val === 'undefined' ||
      val === undefined ||
      val === ''
    )
      return true
    return false
  }
  return false
}

// 统一批量导出
export default other
