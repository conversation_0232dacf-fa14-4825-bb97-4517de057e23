<template>
  <div class="centerRight">
    <n-carousel autoplay :show-dots="false">
      <div v-for="(warehouse, index) in chartData" :key="index">
        <chart :warehouse-data="warehouse" />
      </div>
    </n-carousel>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, ref } from 'vue'
import { NCarousel } from 'naive-ui'
import Chart from './chart/index'

defineComponent({
  components: {
    NCarousel,
    Chart,
  },
})

// 定义多个仓库的图表数据
const chartData = ref([
  {
    name: '物资仓库A',
    temperatureData: [22, 41, 45, 48, 55, 33, 44, 20, 34, 40, 47, 20],
    humidityData: [58, 18, 30, 25, 40, 62, 38, 37, 20, 30, 49, 60],
  },
  {
    name: '物资仓库B',
    temperatureData: [28, 35, 42, 38, 45, 40, 48, 25, 30, 35, 42, 28],
    humidityData: [65, 22, 35, 30, 45, 58, 42, 40, 25, 35, 52, 65],
  },
  {
    name: '物资仓库C',
    temperatureData: [25, 38, 40, 45, 50, 35, 42, 22, 32, 38, 45, 25],
    humidityData: [60, 20, 32, 28, 42, 60, 40, 38, 22, 32, 50, 62],
  },
])


setTimeout(() => {
  chartData.value[0].temperatureData = [0, 11, 2, 11, 1, 3, 4, 0, 4, 0, 3, 54]
}, 6000);
</script>

<style lang="scss" scoped>
.centerRight {
  height: 300px;
  width: 480px;
  position: relative;
}
</style>
