<template>
  <div class="center-left">
    <div class="scroll-container">
      <div v-for="(item, index) in displayData" :key="index" class="progress-item">
        <div class="top-text">
          <div class="progress-label">
            <div style="width: 42px; height: 22px; margin-right: 10px; text-align: left">
              NO.{{ item.originalIndex }}
            </div>
            <div>{{ item.label }}</div>
          </div>
          <div :class="item.originalIndex == 1 ? 'progress-num' : 'progress-nums'">
            12234
          </div>
        </div>
        <div class="progress-container">
          <div
            class="progress-bar"
            :style="getProgressStyle(item.value, item.originalIndex)"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 原始数据
const progressData = ref([
  {
    label: '仓库A',
    value: 85,
  },
  {
    label: '仓库B',
    value: 72,
  },
  {
    label: '仓库C',
    value: 93,
  },
  {
    label: '仓库D',
    value: 68,
  },
  {
    label: '仓库E',
    value: 78,
  },
  {
    label: '仓库F',
    value: 88,
  },
  {
    label: '仓库G',
    value: 65,
  },
  {
    label: '仓库H',
    value: 92,
  },
  {
    label: '仓库I',
    value: 75,
  },
  {
    label: '仓库J',
    value: 83,
  },
])

// 当前显示的起始索引
const currentIndex = ref(0)
const maxDisplayItems = 5

// 计算显示的数据（最多5条，带原始索引）
const displayData = computed(() => {
  const startIndex = currentIndex.value
  const endIndex = startIndex + maxDisplayItems

  return progressData.value.slice(startIndex, endIndex).map((item, index) => ({
    ...item,
    originalIndex: startIndex + index + 1,
  }))
})

// 自动滚动定时器
let scrollTimer = null

// 开始自动滚动
const startAutoScroll = () => {
  scrollTimer = setInterval(() => {
    if (progressData.value.length > maxDisplayItems) {
      currentIndex.value =
        (currentIndex.value + 1) % (progressData.value.length - maxDisplayItems + 1)
    }
  }, 3000) // 每3秒滚动一次
}

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
    scrollTimer = null
  }
}

onMounted(() => {
  startAutoScroll()
})

onUnmounted(() => {
  stopAutoScroll()
})

// 渐变色配置
const gradientColors = [
  'linear-gradient(90deg, #58390A 0%, #F4BF70 73.67%, #FEF6DF 100%)',
  'linear-gradient(90deg, #0F4B38 0%, #22B081 73.67%, #DDFCF2 100%)',
  'linear-gradient(90deg, #45b7d1, #74c7ec)',
  'linear-gradient(90deg, #96ceb4, #b8e6d1)',
]

// 获取进度条样式
const getProgressStyle = (value, index) => {
  return {
    width: `${value}%`,
    background: index == 1 ? gradientColors[0] : gradientColors[1],
    height: '100%',
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20px;
}

.progress-item {
  width: 456px;
  height: 37px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .top-text {
    display: flex;
    justify-content: space-between;
    height: 22px;
    align-items: center;
  }
}

.progress-label {
  display: flex;
  align-items: center;
  color: #dff5ec;
  text-align: center;
  font-family: 'alia';
  font-size: 14px;
  font-style: normal;
  font-weight: 55 Regular;
  line-height: normal;
}
.progress-num {
  text-align: center;
  font-family: 'alia';
  font-size: 16px;
  font-style: normal;
  font-weight: 65 Medium;
  line-height: normal;
  background: linear-gradient(180deg, #ffd699 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.progress-nums {
  text-align: center;
  font-family: 'alia';
  font-size: 16px;
  font-style: normal;
  font-weight: 65 Medium;
  line-height: normal;
  background: linear-gradient(180deg, #99ffd6 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.progress-container {
  position: relative;
  width: 456px;
  height: 10px;
  flex-shrink: 0;
  background: #0a3030;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease-in-out;
}
.center-left {
  width: 456px;
  height: 235px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
  position: relative;
}

.scroll-container .progress-item {
  flex-shrink: 0; /* 防止flex压缩高度 */
  opacity: 1;
  transform: translateY(0);
  transition: all 0.5s ease-in-out;
}

/* 添加进入和离开的动画效果 */
.progress-item-enter-active,
.progress-item-leave-active {
  transition: all 0.5s ease-in-out;
}

.progress-item-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.progress-item-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
</style>
