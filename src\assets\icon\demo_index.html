<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i2/O1CN01ZyAlrn1MwaMhqz36G_!!6000000001499-73-tps-64-64.ico" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01EYTRnJ297D6vehehJ_!!6000000008020-55-tps-64-64.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2091333" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe615;</span>
                <div class="name">统计</div>
                <div class="code-name">&amp;#xe615;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">统计2</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">统计4</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">指标2</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">分析2</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">分析7</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">分析5</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">数据7</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62b;</span>
                <div class="name">应用</div>
                <div class="code-name">&amp;#xe62b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fb;</span>
                <div class="name">lock</div>
                <div class="code-name">&amp;#xe7fb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fd;</span>
                <div class="name">align-left</div>
                <div class="code-name">&amp;#xe7fd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7fe;</span>
                <div class="name">border-bottom</div>
                <div class="code-name">&amp;#xe7fe;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81b;</span>
                <div class="name">cloud download</div>
                <div class="code-name">&amp;#xe81b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81c;</span>
                <div class="name">cloud upload</div>
                <div class="code-name">&amp;#xe81c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe86a;</span>
                <div class="name">rank</div>
                <div class="code-name">&amp;#xe86a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe86e;</span>
                <div class="name">early-warning</div>
                <div class="code-name">&amp;#xe86e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe888;</span>
                <div class="name">vector</div>
                <div class="code-name">&amp;#xe888;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88e;</span>
                <div class="name">monitoring</div>
                <div class="code-name">&amp;#xe88e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe88f;</span>
                <div class="name">diagnose</div>
                <div class="code-name">&amp;#xe88f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe892;</span>
                <div class="name">Directory tree</div>
                <div class="code-name">&amp;#xe892;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe89e;</span>
                <div class="name">application</div>
                <div class="code-name">&amp;#xe89e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe777;</span>
                <div class="name">supervise</div>
                <div class="code-name">&amp;#xe777;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78c;</span>
                <div class="name">chart-pie-alt</div>
                <div class="code-name">&amp;#xe78c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78f;</span>
                <div class="name">chart-area</div>
                <div class="code-name">&amp;#xe78f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">chart-line</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">chart-bar</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe797;</span>
                <div class="name">laptop</div>
                <div class="code-name">&amp;#xe797;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f7;</span>
                <div class="name">layer-group</div>
                <div class="code-name">&amp;#xe7f7;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1620956942234') format('woff2'),
       url('iconfont.woff?t=1620956942234') format('woff'),
       url('iconfont.ttf?t=1620956942234') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-tongji"></span>
            <div class="name">
              统计
            </div>
            <div class="code-name">.icon-tongji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongji2"></span>
            <div class="name">
              统计2
            </div>
            <div class="code-name">.icon-tongji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tongji4"></span>
            <div class="name">
              统计4
            </div>
            <div class="code-name">.icon-tongji4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhibiao2"></span>
            <div class="name">
              指标2
            </div>
            <div class="code-name">.icon-zhibiao2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxi2"></span>
            <div class="name">
              分析2
            </div>
            <div class="code-name">.icon-fenxi2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxi7"></span>
            <div class="name">
              分析7
            </div>
            <div class="code-name">.icon-fenxi7
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxi5"></span>
            <div class="name">
              分析5
            </div>
            <div class="code-name">.icon-fenxi5
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shuju7"></span>
            <div class="name">
              数据7
            </div>
            <div class="code-name">.icon-shuju7
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingyong"></span>
            <div class="name">
              应用
            </div>
            <div class="code-name">.icon-yingyong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-lock"></span>
            <div class="name">
              lock
            </div>
            <div class="code-name">.icon-lock
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-align-left"></span>
            <div class="name">
              align-left
            </div>
            <div class="code-name">.icon-align-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-border-bottom"></span>
            <div class="name">
              border-bottom
            </div>
            <div class="code-name">.icon-border-bottom
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-clouddownload"></span>
            <div class="name">
              cloud download
            </div>
            <div class="code-name">.icon-clouddownload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-cloudupload"></span>
            <div class="name">
              cloud upload
            </div>
            <div class="code-name">.icon-cloudupload
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rank"></span>
            <div class="name">
              rank
            </div>
            <div class="code-name">.icon-rank
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-early-warning"></span>
            <div class="name">
              early-warning
            </div>
            <div class="code-name">.icon-early-warning
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-vector"></span>
            <div class="name">
              vector
            </div>
            <div class="code-name">.icon-vector
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-monitoring"></span>
            <div class="name">
              monitoring
            </div>
            <div class="code-name">.icon-monitoring
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-diagnose"></span>
            <div class="name">
              diagnose
            </div>
            <div class="code-name">.icon-diagnose
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-Directory-tree"></span>
            <div class="name">
              Directory tree
            </div>
            <div class="code-name">.icon-Directory-tree
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-application"></span>
            <div class="name">
              application
            </div>
            <div class="code-name">.icon-application
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-supervise"></span>
            <div class="name">
              supervise
            </div>
            <div class="code-name">.icon-supervise
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart-pie-alt"></span>
            <div class="name">
              chart-pie-alt
            </div>
            <div class="code-name">.icon-chart-pie-alt
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart-area"></span>
            <div class="name">
              chart-area
            </div>
            <div class="code-name">.icon-chart-area
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart-line"></span>
            <div class="name">
              chart-line
            </div>
            <div class="code-name">.icon-chart-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chart-bar"></span>
            <div class="name">
              chart-bar
            </div>
            <div class="code-name">.icon-chart-bar
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-laptop"></span>
            <div class="name">
              laptop
            </div>
            <div class="code-name">.icon-laptop
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-layer-group"></span>
            <div class="name">
              layer-group
            </div>
            <div class="code-name">.icon-layer-group
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongji"></use>
                </svg>
                <div class="name">统计</div>
                <div class="code-name">#icon-tongji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongji2"></use>
                </svg>
                <div class="name">统计2</div>
                <div class="code-name">#icon-tongji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tongji4"></use>
                </svg>
                <div class="name">统计4</div>
                <div class="code-name">#icon-tongji4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhibiao2"></use>
                </svg>
                <div class="name">指标2</div>
                <div class="code-name">#icon-zhibiao2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxi2"></use>
                </svg>
                <div class="name">分析2</div>
                <div class="code-name">#icon-fenxi2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxi7"></use>
                </svg>
                <div class="name">分析7</div>
                <div class="code-name">#icon-fenxi7</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxi5"></use>
                </svg>
                <div class="name">分析5</div>
                <div class="code-name">#icon-fenxi5</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shuju7"></use>
                </svg>
                <div class="name">数据7</div>
                <div class="code-name">#icon-shuju7</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingyong"></use>
                </svg>
                <div class="name">应用</div>
                <div class="code-name">#icon-yingyong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-lock"></use>
                </svg>
                <div class="name">lock</div>
                <div class="code-name">#icon-lock</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-align-left"></use>
                </svg>
                <div class="name">align-left</div>
                <div class="code-name">#icon-align-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-border-bottom"></use>
                </svg>
                <div class="name">border-bottom</div>
                <div class="code-name">#icon-border-bottom</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-clouddownload"></use>
                </svg>
                <div class="name">cloud download</div>
                <div class="code-name">#icon-clouddownload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-cloudupload"></use>
                </svg>
                <div class="name">cloud upload</div>
                <div class="code-name">#icon-cloudupload</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rank"></use>
                </svg>
                <div class="name">rank</div>
                <div class="code-name">#icon-rank</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-early-warning"></use>
                </svg>
                <div class="name">early-warning</div>
                <div class="code-name">#icon-early-warning</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-vector"></use>
                </svg>
                <div class="name">vector</div>
                <div class="code-name">#icon-vector</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-monitoring"></use>
                </svg>
                <div class="name">monitoring</div>
                <div class="code-name">#icon-monitoring</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-diagnose"></use>
                </svg>
                <div class="name">diagnose</div>
                <div class="code-name">#icon-diagnose</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Directory-tree"></use>
                </svg>
                <div class="name">Directory tree</div>
                <div class="code-name">#icon-Directory-tree</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-application"></use>
                </svg>
                <div class="name">application</div>
                <div class="code-name">#icon-application</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-supervise"></use>
                </svg>
                <div class="name">supervise</div>
                <div class="code-name">#icon-supervise</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart-pie-alt"></use>
                </svg>
                <div class="name">chart-pie-alt</div>
                <div class="code-name">#icon-chart-pie-alt</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart-area"></use>
                </svg>
                <div class="name">chart-area</div>
                <div class="code-name">#icon-chart-area</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart-line"></use>
                </svg>
                <div class="name">chart-line</div>
                <div class="code-name">#icon-chart-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chart-bar"></use>
                </svg>
                <div class="name">chart-bar</div>
                <div class="code-name">#icon-chart-bar</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-laptop"></use>
                </svg>
                <div class="name">laptop</div>
                <div class="code-name">#icon-laptop</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-layer-group"></use>
                </svg>
                <div class="name">layer-group</div>
                <div class="code-name">#icon-layer-group</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
