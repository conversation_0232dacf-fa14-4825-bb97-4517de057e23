<template>
  <div class="centerRight2">
    <div>
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="displayData"
        :bordered="false"
        :bottom-bordered="false"
        :virtual-scroll="true"
        size="small"
        :single-line="false"
        :max-height="214"
        :row-class-name="getRowClassName" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, h, onMounted, onUnmounted, computed } from 'vue'
import { NDataTable } from 'naive-ui'
// @ts-ignore
import numOne from '@/assets/numOne.png'
// @ts-ignore
import numx from '@/assets/numx.png'
// @ts-ignore
import numTwo from '@/assets/numTwo.png'
// @ts-ignore
import numThree from '@/assets/numThree.png'

function createColumns() {
  return [
    {
      title: '序号',
      key: 'key',
      align: 'center',
      width: 100,
      render(row: any) {
        if (row.key === 1) {
          return h('img', {
            src: numOne,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else if (row.key === 2) {
          return h('img', {
            src: numTwo,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else if (row.key === 3) {
          return h('img', {
            src: numThree,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else {
          return h(
            'div',
            {
              style: {
                backgroundImage: `url(${numx})`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
                width: '80px',
                height: '26px',

                color: '#fff',
              },
            },
            row.key.toString(),
          )
        }
      },
    },
    {
      title: '仓库',
      key: 'name',
      align: 'center',
    },
    {
      title: '物资名称',
      key: 'age',
      width: 400,
      ellipsis: true,
    },
    {
      title: '库存/预警值',
      key: 'address',
      align: 'center',
    },
  ]
}

function createData() {
  return [
    {
      key: 1,
      name: '物资仓库A',
      age: '大开杀戒代付款盛世嫡妃金卡拉萨的反啊实打实大伤口大数据库击看亮机卡',

      address: '22/33',
    },
    {
      key: 2,
      name: '到付件可视角度',
      age: '尽快来得及防守打法金坷垃就开了防守打法监考老师',
      address: '123/456',
    },
    {
      key: 3,
      name: 'Joe Black',
      age: 32,
      address: '66/2123',
    },
    {
      key: 4,
      name: '仓库e',
      age: 32,
      address: '4444/6565',
    },
    {
      key: 5,
      name: '仓库c',
      age: '大开杀戒代付款盛世嫡妃金卡拉萨的反击看亮机卡',
      address: '664/6743',
    },
    {
      key: 6,
      name: '仓库d',
      age: 32,
      address: '234/654',
    },
    {
      key: 7,
      name: 'Joe Black',
      age: 32,

      address: '11345/2323',
    },
  ]
}

export default defineComponent({
  components: {
    NDataTable,
  },
  setup() {
    const tableRef = ref()
    const scrollTimer = ref<number | null>(null)
    const currentIndex = ref(0)
    const itemsPerPage = 5
    const scrollInterval = 3000

    const data = createData()

    const displayData = computed(() => {
      if (data.length <= itemsPerPage) {
        return data
      }

      const result = []
      for (let i = 0; i < itemsPerPage; i++) {
        const index = (currentIndex.value + i) % data.length
        result.push(data[index])
      }
      return result
    })

    // 开始自动滚动
    const startAutoScroll = () => {
      if (data.length <= itemsPerPage) return

      scrollTimer.value = setInterval(() => {
        currentIndex.value = (currentIndex.value + 1) % data.length
      }, scrollInterval)
    }

    // 停止自动滚动
    const stopAutoScroll = () => {
      if (scrollTimer.value) {
        clearInterval(scrollTimer.value)
        scrollTimer.value = null
      }
    }

    onMounted(() => {
      startAutoScroll()
    })

    onUnmounted(() => {
      stopAutoScroll()
    })

    // 根据数据的key值设置行类名
    const getRowClassName = (rowData: any) => {
      return rowData.key % 2 === 1 ? 'odd-row' : 'even-row'
    }

    return {
      tableRef,
      displayData,
      columns: createColumns(),
      getRowClassName,
    }
  },
})
</script>

<style lang="scss" scoped>
$box-height: 257px;
$box-width: 812px;
.centerRight2 {
  height: $box-height;
  width: $box-width;
}

:deep(.n-data-table .n-data-table-th) {
  color: #effff9;
  text-align: center;
  font-family: 'alia';
  font-size: 14px;
  border: none;
  background-color: #0b3a31;
}

:deep(.n-data-table .n-data-table-td) {
  border: none;
  background-color: #0b3a31;
  color: #effff9;
  font-family: 'alia';
  font-size: 14px;
}

:deep(.n-data-table .n-data-table-table) {
  width: 100%;
  word-break: break-word;
  border-collapse: separate; /* 使用separate来创建行间隙 */
  border-spacing: 0 4px; /* 列间隙为0，行间隙为4px，减小间距 */
  background-color: transparent; /* 表格背景透明 */
  margin-bottom: -4px !important;
  color: #effff9 !important;
}

/* 扩大行间隙 */
:deep(.n-data-table .n-data-table-tr) {
  background-color: transparent; /* 行背景透明 */
  height: 36px; /* 设置行高为36px */
}

:deep(.n-data-table .n-data-table-td),
:deep(.n-data-table .n-data-table-th) {
  padding: 6px; /* 调整内边距 */
  height: 36px; /* 设置行高为36px */
  line-height: 20px; /* 设置行内文字行高 */
}

/* 确保表头和表体之间间距一致 */
:deep(.n-data-table .n-data-table-thead) {
  margin-bottom: 0;
}

:deep(.n-data-table .n-data-table-tbody) {
  margin-top: 0;
}

:deep(.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover) {
  background-color: #0b3a31 !important;
}

:deep(.n-data-table
    .n-data-table-tr:not(.n-data-table-tr--summary):hover
    .n-data-table-td) {
  background-color: #0b3a31 !important;
}

/* 奇数行背景色设置 */
:deep(.n-data-table .n-data-table-tbody .n-data-table-tr.odd-row .n-data-table-td) {
  background-color: #093029 !important;
}

/* 偶数行背景色保持不变 */
:deep(.n-data-table .n-data-table-tbody .n-data-table-tr.even-row .n-data-table-td) {
  background-color: #0b3a31 !important;
}
</style>
