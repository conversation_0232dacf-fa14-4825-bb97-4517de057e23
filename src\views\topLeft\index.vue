<template>
  <div class="centerRight2">
    <n-carousel autoplay :show-dots="false">
      <div v-for="(warehouse, index) in data" :key="index">
        <div class="top">
          <div class="top_name">{{ warehouse.name }}</div>
          <div class="top_content">
            <div><img src="@/assets/wdIcon.png" style="width: 28px; height: 28px" /></div>
            <div style="margin-left: 6px">温度</div>
            <div style="margin-left: 18px">
              <img src="@/assets/sdIcon.png" style="width: 28px; height: 28px" />
            </div>
            <div style="margin-right: 18px; margin-left: 6px">湿度</div>
            <div><img src="@/assets/afIcon.png" style="width: 28px; height: 28px" /></div>
            <div style="margin-left: 6px">安防</div>
          </div>
        </div>

        <div class="bottom">
          <div class="bottom1">
            <div class="text">仓库区数</div>
            <div class="num">
              {{ warehouse.qs }}
            </div>
          </div>
          <div class="bottom2">
            <div class="text">仓库列数</div>
            <div class="num">
              {{ warehouse.ls }}
            </div>
          </div>
          <div class="bottom3">
            <div class="text">仓库货位数</div>
            <div class="num">
              {{ warehouse.hws }}
            </div>
          </div>
          <div class="bottom4">
            <div class="text">空间利用率</div>
            <div class="num">
              {{ warehouse.lyl }}
            </div>
          </div>
        </div>
      </div>
    </n-carousel>
  </div>
</template>

<script setup>
import { defineComponent, reactive, ref } from 'vue'
import { NCarousel } from 'naive-ui'
defineComponent({
  components: {
    NCarousel,
  },
})
let data = ref([
  {
    name: '物资仓库A',
    sd: '1',
    wd: '1',
    an: '1',

    qs: '34',
    ls: '26',
    hws: '984322',
    lyl: '892',
  },
  {
    name: '物资仓库B',
    sd: '1',
    wd: '1',
    an: '1',

    qs: '34',
    ls: '26',
    hws: '984322',
    lyl: '892',
  },
  {
    name: '物资仓库C',
    sd: '1',
    wd: '1',
    an: '1',

    qs: '34',
    ls: '26',
    hws: '984322',
    lyl: '892',
  },
])
</script>

<style lang="scss" scoped>
$box-height: 267px;
$box-width: 480px;
.centerRight2 {
  height: $box-height;
  width: $box-width;
  position: relative;
}
.top {
  position: absolute;
  top: 20px;
  width: 480px;
  padding-left: 17px;
  padding-right: 17px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .top_name {
    font-family: 'alia';
    font-size: 16px;
    font-style: normal;
    font-weight: 85 Bold;
    line-height: normal;
    background: linear-gradient(180deg, #ffcd61 0%, #fff 79.21%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .top_content {
    width: 210px;
    display: flex;
    align-items: center;
    color: #eff7ff;
    font-family: 'alia';
    font-size: 12px;
  }
}
.bottom {
  width: 480px;
  height: 178px;
  padding-left: 17px;
  padding-right: 17px;
  position: absolute;
  bottom: 17px;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
  //换行
  flex-wrap: wrap;

  .bottom1 {
    width: 210px;
    height: 76px;
    background-image: url('@/assets/qs.png');
    background-size: 210px 76px;
    background-repeat: no-repeat;
    padding-left: 80px;
    padding-top: 16px;
    padding-bottom: 11px;
    box-sizing: border-box;
  }
  .bottom2 {
    width: 210px;
    height: 76px;
    background-image: url('@/assets/ls.png');
    background-size: 210px 76px;
    background-repeat: no-repeat;
    padding-left: 80px;
    padding-top: 16px;
    padding-bottom: 11px;
    box-sizing: border-box;
  }
  .bottom3 {
    width: 210px;
    height: 76px;
    background-image: url('@/assets/hws.png');
    background-size: 210px 76px;
    background-repeat: no-repeat;
    padding-left: 80px;
    padding-top: 16px;
    padding-bottom: 11px;
    box-sizing: border-box;
  }
  .bottom4 {
    width: 210px;
    height: 76px;
    background-image: url('@/assets/lyl.png');
    background-size: 210px 76px;
    background-repeat: no-repeat;
    padding-left: 80px;
    padding-top: 16px;
    padding-bottom: 11px;
    box-sizing: border-box;
  }
}

.text {
  color: #e6fff5;
  font-family: 'alia';
  font-size: 14px;
  font-style: normal;
}
.num {
  font-family: 'Alib';
  font-size: 22px;
  font-style: normal;
  font-weight: 75 SemiBold;
  line-height: normal;
  background: linear-gradient(180deg, #84ffce 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
